defmodule RivaAshWeb.DashboardLive do
  @moduledoc """
  Main dashboard LiveView with statistics and quick actions.
  """
  use RivaAshWeb, :live_view

  # Explicitly set the authenticated layout
  @layout {RivaAshWeb.Layouts, :authenticated}

  alias RivaAsh.ErrorHelpers

  import RivaAshWeb.Components.Organisms.PageHeader
  import RivaAshWeb.Components.Organisms.DashboardStats
  import RivaAshWeb.Components.Organisms.CalendarView
  import RivaAshWeb.Live.AuthHelpers

  @impl true
  def mount(_params, session, socket) do
    case get_current_user_from_session(session) do
      {:ok, user} ->
        socket =
          socket
          |> assign(:current_user, user)
          |> assign(:page_title, "Dashboard")
          |> assign(:stats, [])
          |> assign(:recent_reservations, [])
          |> assign(:upcoming_events, [])

        {:ok, socket}
      {:error, _} ->
        {:ok, redirect(socket, to: "/sign-in")}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <!-- Dashboard implementation will go here -->
    <div>
      <.page_header title="Dashboard" description="Overview of your business operations" />

      <.dashboard_stats stats={@stats} />

      <div>
        <h2>Recent Activity</h2>
        <!-- Recent activity content -->
      </div>

      <div>
        <h2>Quick Actions</h2>
        <!-- Quick action buttons -->
      </div>
    </div>
    """
  end

  @impl true
  def handle_event(_event, _params, socket) do
    {:noreply, socket}
  end
end
